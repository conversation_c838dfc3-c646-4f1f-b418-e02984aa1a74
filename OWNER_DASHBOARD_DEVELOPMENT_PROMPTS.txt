================================================================================
                    OWNER DASHBOARD GELİŞTİRME PROMPT'LARI
================================================================================

PROJE: GymKod Pro - Spor Salonu Yönetim Sistemi
TARİH: 2025-06-24
AMAÇ: Owner yetkisi ile admin'leri kontrol edebileceğim kapsamlı dashboard sistemi

MEVCUT PROJE YAPISI:
- Frontend: Angular 17.3.6, TypeScript, Bootstrap + Modern CSS
- Backend: .NET Core, Entity Framework, JWT Authentication
- Database: SQL Server, Multi-tenant architecture
- Tasarım: modern-card, modern-btn, modern-table CSS class'ları
- Roller: owner, admin, member
- Lisans Sistemi: UserLicense, LicensePackage, LicenseTransaction

================================================================================
PROMPT 1: ANA OWNER DASHBOARD COMPONENT'İ OLUŞTURMA
================================================================================

GÖREV: Owner hesabıyla giriş yaptığımda göreceğim ana dashboard sayfasını oluştur.

DETAYLAR:
- Route: /owner-dashboard
- Component Adı: OwnerDashboardComponent
- Tasarım: 4'lü grid layout (2x2)
- Referans: allmembers ve memberfilter component'lerinin kart tasarımı
- CSS: modern-card, modern-btn class'larını kullan

DASHBOARD KARTLARI:

1. TOPLAM SPOR SALONU SAYISI KARTI:
   - Büyük sayı: Toplam aktif salon sayısı (Company tablosundan)
   - Alt bilgi: "Aktif Salonlar"
   - İkon: fas fa-dumbbell
   - Renk: Primary blue (modern-card-primary)
   - Tıklanabilir: Evet → /active-gyms sayfasına yönlendir

2. AKTİF ADMİNLER (LİSANSLI) KARTI:
   - Büyük sayı: Aktif lisansa sahip admin sayısı (UserLicense tablosundan)
   - Alt bilgi: "Lisanslı Adminler"
   - İkon: fas fa-user-shield
   - Renk: Success green (modern-card-success)
   - Tıklanabilir: Evet → /licensed-admins sayfasına yönlendir

3. BU AY TOPLAM GELİR KARTI:
   - Büyük sayı: Bu ay toplam gelir (LicenseTransaction tablosundan)
   - Alt bilgi: "Bu Ay Toplam Gelir"
   - İkon: fas fa-chart-line
   - Renk: Warning orange (modern-card-warning)
   - Tıklanabilir: Evet → /owner-revenue sayfasına yönlendir

4. YAKINDA BİTECEK LİSANSLAR KARTI:
   - Büyük sayı: 30 gün içinde bitecek lisans sayısı
   - Alt bilgi: "Yakında Bitecek Lisanslar"
   - İkon: fas fa-exclamation-triangle
   - Renk: Danger red (modern-card-danger)
   - Tıklanabilir: Evet → /expiring-licenses sayfasına yönlendir

BACKEND REQUİREMENT:
- OwnerDashboardController oluştur
- GetDashboardStats endpoint'i ekle
- Owner yetkisi kontrolü ekle [SecuredOperation("owner")]
- OwnerDashboardStatsDto oluştur

FRONTEND REQUİREMENT:
- OwnerDashboardComponent oluştur (standalone: false)
- OwnerDashboardService oluştur
- Responsive tasarım (mobilde tek sütun)
- Loading state'leri ekle (app-loading-spinner)
- Error handling ekle

CSS REQUİREMENT:
- Mevcut modern-components.css ile uyumlu
- Hover efektleri (transform: translateY(-5px))
- Card shadow'ları (var(--shadow-md))
- Dark mode desteği ([data-theme="dark"])

ROUTING REQUİREMENT:
- app-routing.module.ts'ye route ekle
- roleGuard ile owner yetkisi kontrolü
- Sidebar'a "Owner Dashboard" linki ekle (gymMenuOpen altına)

================================================================================
PROMPT 2: AKTİF SALONLAR (LİSANSLI ADMİNLER) LİSTESİ SAYFASI
================================================================================

GÖREV: Admin'leri allmembers sayfası gibi listeleyen ve yöneten sayfa oluştur.

DETAYLAR:
- Route: /licensed-admins
- Component Adı: LicensedAdminsComponent
- Tasarım Referansı: allmembers component'i ile aynı yapı
- Amaç: Admin'leri member'lar gibi kontrol etmek

TABLO YAPISI (modern-table class'ı):
- Salon Adı (Company.Name)
- Admin Adı (User.FirstName + LastName)
- Telefon (CompanyUser.Phone)
- Email (User.Email)
- Lisans Paketi (LicensePackage.PackageName)
- Kalan Gün (renk kodlamalı badge)
- Durum (Aktif/Pasif modern-badge)
- İşlemler (modern-btn-sm butonları)

FİLTRELEME ÖZELLİKLERİ:
- Lisans paketine göre dropdown (modern-form-control)
- Şehire göre dropdown (City tablosundan)
- Duruma göre (Aktif/Pasif) dropdown
- Kalan güne göre (0-7, 8-30, 30+ gün)
- Arama: Salon adı, admin adı, telefon (modern-form-control)

SAYFALAMA:
- allmembers gibi pagination
- Sayfa başına 10-20-50 kayıt seçenekleri
- Toplam kayıt sayısı gösterimi

İŞLEM BUTONLARI:
1. DETAY BUTONU (modern-btn-info):
   - AdminDetailDialogComponent modal'ı aç
   - member-detail-dialog gibi tasarım

2. DÜZENLE BUTONU (modern-btn-warning):
   - Admin bilgilerini güncelleme modal'ı
   - Salon bilgileri, iletişim bilgileri

3. LİSANS UZAT BUTONU (modern-btn-success):
   - Hızlı lisans uzatma modal'ı
   - Süre seçimi (30, 60, 90 gün)

4. PASİF YAP BUTONU (modern-btn-danger):
   - Salonu geçici devre dışı bırakma
   - Onay dialog'u (ConfirmationDialogComponent)

BACKEND REQUİREMENT:
- GetLicensedAdmins endpoint'i
- Filtreleme parametreleri
- Pagination desteği (PaginatedResult)
- Admin güncelleme endpoint'leri

FRONTEND REQUİREMENT:
- LicensedAdminsComponent (standalone: false)
- AdminDetailDialogComponent
- AdminUpdateComponent
- LicenseExtendComponent

================================================================================
PROMPT 3: ADMİN DETAY MODAL COMPONENT'İ
================================================================================

GÖREV: member-detail-dialog gibi admin detaylarını gösteren modal oluştur.

DETAYLAR:
- Component Adı: AdminDetailDialogComponent
- Tasarım Referansı: member-detail-dialog component'i
- Modal boyutu: 95% width, 85vh height
- Responsive: Mobilde tam ekran

SEKMELER (TABS) - Bootstrap nav-tabs:
1. KİŞİSEL BİLGİLER:
   - Profil fotoğrafı (FileUploadService)
   - Ad Soyad (User.FirstName + LastName)
   - Email (User.Email)
   - Telefon (CompanyUser.Phone)
   - Kayıt tarihi (User.CreationDate)
   - Son giriş tarihi

2. SALON BİLGİLERİ:
   - Salon adı (Company.Name)
   - Salon adresi (CompanyAdress)
   - Salon telefonu (Company.Phone)
   - Şehir/İlçe (City/Town)
   - Salon durumu
   - Üye sayısı (Member count)

3. LİSANS GEÇMİŞİ:
   - Mevcut lisans paketi (UserLicense)
   - Başlangıç tarihi
   - Bitiş tarihi
   - Kalan gün (renk kodlamalı)
   - Geçmiş lisanslar tablosu (modern-table)

4. ÖDEME GEÇMİŞİ:
   - Ödeme tablosu (LicenseTransaction)
   - Toplam ödenen tutar
   - Son ödeme tarihi
   - Ödeme yöntemi

5. SİSTEM LOGLARI:
   - Giriş-çıkış logları
   - İşlem geçmişi
   - Güvenlik logları

BACKEND REQUİREMENT:
- GetAdminDetailById endpoint'i
- Admin license history endpoint'i
- Admin payment history endpoint'i
- Admin activity logs endpoint'i

FRONTEND REQUİREMENT:
- AdminDetailDialogComponent (standalone: false)
- Tab navigation (Bootstrap)
- Data loading states (app-loading-spinner)
- Error handling

================================================================================
PROMPT 4: TOPLAM GELİR SAYFASI (OWNER REVENUE)
================================================================================

GÖREV: paymenthistory sayfası gibi gelir takibi sayfası oluştur.

DETAYLAR:
- Route: /owner-revenue
- Component Adı: OwnerRevenueComponent
- Tasarım Referansı: paymenthistory component'i
- Amaç: Tüm salonlardan gelen geliri takip etmek

ÜST KISIM İSTATİSTİKLER (4'lü kart - modern-card):
1. Bu Ay Toplam Gelir (modern-card-primary)
2. Geçen Ay Karşılaştırması (% artış/azalış)
3. Yıllık Toplam Gelir (modern-card-success)
4. Ortalama Aylık Gelir (modern-card-info)

GRAFİK BÖLÜMÜ:
1. Aylık Gelir Trendi (Line Chart):
   - Son 12 ay gelir grafiği
   - Chart.js kullan

2. Salon Bazlı Gelir Dağılımı (Bar Chart):
   - En karlı 10 salon
   - Horizontal bar chart

3. Lisans Paketi Satış Dağılımı (Pie Chart):
   - Admin Basic, Premium vs. dağılımı

DETAYLI TABLO (modern-table):
- Tarih (LicenseTransaction.TransactionDate)
- Salon Adı (Company.Name)
- Admin Adı (User.FirstName + LastName)
- Lisans Paketi (LicensePackage.PackageName)
- Tutar (₺ formatında)
- Ödeme Yöntemi
- Durum (Başarılı/Başarısız modern-badge)

FİLTRELEME:
- Tarih aralığı (date picker)
- Salon bazlı dropdown
- Lisans paketi bazlı dropdown
- Ödeme yöntemi dropdown
- Durum dropdown

EXPORT ÖZELLİKLERİ:
- Excel export (modern-btn-success)
- PDF export (modern-btn-danger)
- CSV export (modern-btn-info)

BACKEND REQUİREMENT:
- GetRevenueStats endpoint'i
- GetRevenueHistory endpoint'i
- Revenue filtering
- Export endpoints

FRONTEND REQUİREMENT:
- OwnerRevenueComponent (standalone: false)
- Chart.js entegrasyonu
- Export functionality
- Date range picker

================================================================================
PROMPT 5: YAKINDA BİTECEK LİSANSLAR SAYFASI
================================================================================

GÖREV: memberremainingday sayfası gibi lisans takibi sayfası oluştur.

DETAYLAR:
- Route: /expiring-licenses
- Component Adı: ExpiringLicensesComponent
- Tasarım Referansı: memberremainingday component'i
- Amaç: Yakında bitecek lisansları takip etmek

TABLO YAPISI (modern-table):
- Salon Adı (Company.Name)
- Admin Adı (User.FirstName + LastName)
- Telefon (CompanyUser.Phone)
- Email (User.Email)
- Lisans Paketi (LicensePackage.PackageName)
- Kalan Gün (renk kodlamalı modern-badge)
- Bitiş Tarihi (UserLicense.EndDate)
- İşlemler (modern-btn-sm)

RENK KODLAMASI (modern-badge):
- Kırmızı: 0-7 gün (modern-badge-danger)
- Turuncu: 8-15 gün (modern-badge-warning)
- Sarı: 16-30 gün (modern-badge-info)
- Yeşil: 30+ gün (modern-badge-success)

FİLTRELEME:
- Kalan gün aralığı (0-7, 8-15, 16-30, 30+)
- Lisans paketi dropdown
- Şehir dropdown
- Salon durumu dropdown

SIRALAMA:
- Kalan güne göre (artan/azalan)
- Bitiş tarihine göre
- Salon adına göre

İŞLEM BUTONLARI:
1. WHATSAPP BUTONU (modern-btn-success):
   - Otomatik hatırlatma mesajı
   - Şablon mesaj kullan

2. EMAIL BUTONU (modern-btn-info):
   - Hatırlatma emaili gönder
   - Email şablonu kullan

3. UZAT BUTONU (modern-btn-warning):
   - Hızlı lisans uzatma
   - 30/60/90 gün seçenekleri

4. DETAY BUTONU (modern-btn-primary):
   - AdminDetailDialogComponent aç

TOPLU İŞLEMLER:
- Checkbox ile çoklu seçim
- Toplu WhatsApp gönderimi
- Toplu email gönderimi
- Toplu lisans uzatma

BACKEND REQUİREMENT:
- GetExpiringLicenses endpoint'i
- WhatsApp API entegrasyonu
- Email service
- Bulk operations

FRONTEND REQUİREMENT:
- ExpiringLicensesComponent (standalone: false)
- Bulk selection
- WhatsApp integration
- Email templates

================================================================================
PROMPT 6: BACKEND API CONTROLLER'LARI VE SERVİSLER
================================================================================

GÖREV: Owner dashboard için gerekli tüm API endpoint'lerini oluştur.

CONTROLLER: OwnerDashboardController

ENDPOINT'LER:

1. GET /api/ownerdashboard/dashboard-stats
   - Dönen Data: Toplam salon, admin, gelir, yakında bitecek lisans sayıları
   - Yetki: [SecuredOperation("owner")]
   - Response: OwnerDashboardStatsDto

2. GET /api/ownerdashboard/licensed-admins
   - Parametreler: page, size, search, licenseType, city, status
   - Dönen Data: PaginatedResult<LicensedAdminDto>
   - Yetki: [SecuredOperation("owner")]

3. GET /api/ownerdashboard/admin-detail/{id}
   - Dönen Data: AdminDetailDto (kişisel, salon, lisans, ödeme bilgileri)
   - Yetki: [SecuredOperation("owner")]

4. GET /api/ownerdashboard/revenue-stats
   - Parametreler: startDate, endDate
   - Dönen Data: RevenueStatsDto (grafik verileri dahil)
   - Yetki: [SecuredOperation("owner")]

5. GET /api/ownerdashboard/revenue-history
   - Parametreler: page, size, startDate, endDate, gymId, licenseType
   - Dönen Data: PaginatedResult<RevenueHistoryDto>
   - Yetki: [SecuredOperation("owner")]

6. GET /api/ownerdashboard/expiring-licenses
   - Parametreler: days (7,15,30), page, size
   - Dönen Data: PaginatedResult<ExpiringLicenseDto>
   - Yetki: [SecuredOperation("owner")]

7. POST /api/ownerdashboard/extend-license
   - Body: { licenseId, extensionDays }
   - Yetki: [SecuredOperation("owner")]
   - Response: IResult

8. POST /api/ownerdashboard/send-reminder
   - Body: { adminIds[], type: "whatsapp"|"email" }
   - Yetki: [SecuredOperation("owner")]
   - Response: IResult

DTO'LAR (Entities/DTOs klasörüne):
- OwnerDashboardStatsDto
- LicensedAdminDto
- AdminDetailDto
- RevenueStatsDto
- RevenueHistoryDto
- ExpiringLicenseDto

BUSINESS LAYER:
- IOwnerDashboardService interface (Business/Abstract)
- OwnerDashboardManager (Business/Concrete)
- [SecuredOperation("owner")] aspect'leri
- [PerformanceAspect(3)] aspect'leri
- [LogAspect] aspect'leri

DATA ACCESS LAYER:
- IOwnerDashboardDal interface (DataAccess/Abstract)
- EfOwnerDashboardDal (DataAccess/Concrete/EntityFramework)
- EfEntityRepositoryBase'den inherit
- Complex query'ler için custom metotlar

================================================================================
PROMPT 7: FRONTEND SERVİSLER VE MODELLER
================================================================================

GÖREV: Owner dashboard için gerekli Angular servisler ve modeller oluştur.

SERVİSLER (src/app/services):

1. OwnerDashboardService:
   - getDashboardStats(): Observable<SingleResponseModel<OwnerDashboardStats>>
   - getLicensedAdmins(params): Observable<ListResponseModel<LicensedAdmin>>
   - getAdminDetail(id): Observable<SingleResponseModel<AdminDetail>>
   - updateAdmin(admin): Observable<ResponseModel>
   - extendLicense(licenseId, days): Observable<ResponseModel>

2. OwnerRevenueService:
   - getRevenueStats(dateRange): Observable<SingleResponseModel<RevenueStats>>
   - getRevenueHistory(params): Observable<ListResponseModel<RevenueHistory>>
   - exportRevenue(format, params): Observable<Blob>

3. ExpiringLicensesService:
   - getExpiringLicenses(params): Observable<ListResponseModel<ExpiringLicense>>
   - sendReminder(adminIds, type): Observable<ResponseModel>
   - bulkExtendLicenses(licenses): Observable<ResponseModel>

MODELLER (src/app/models):

1. OwnerDashboardStats:
   - totalGyms: number
   - totalLicensedAdmins: number
   - monthlyRevenue: number
   - expiringLicenses: number

2. LicensedAdmin:
   - adminId: number
   - gymName: string
   - adminName: string
   - phone: string
   - email: string
   - licensePackage: string
   - remainingDays: number
   - status: string
   - city: string

3. AdminDetail:
   - personalInfo: AdminPersonalInfo
   - gymInfo: GymInfo
   - licenseHistory: LicenseHistory[]
   - paymentHistory: PaymentHistory[]
   - activityLogs: ActivityLog[]

4. RevenueStats:
   - monthlyRevenue: number
   - previousMonthComparison: number
   - yearlyRevenue: number
   - averageMonthlyRevenue: number
   - chartData: ChartData

5. ExpiringLicense:
   - licenseId: number
   - gymName: string
   - adminName: string
   - phone: string
   - email: string
   - licensePackage: string
   - remainingDays: number
   - expiryDate: Date

BASE SERVİS YAPISI:
- BaseApiService'den inherit
- HttpClient kullanımı
- Error handling (catchError)
- Loading states
- Token management (otomatik)

RESPONSE MODELLER:
- SingleResponseModel<T>
- ListResponseModel<T>
- ResponseModel
- PaginatedResponseModel<T>

================================================================================
PROMPT 8: ROUTING VE NAVİGASYON GÜNCELLEMELERİ
================================================================================

GÖREV: Owner dashboard için routing ve navigasyon güncellemelerini yap.

APP-ROUTING.MODULE.TS GÜNCELLEMELERİ:

Yeni route'lar ekle (mevcut route'ların arasına):

```typescript
// Owner Dashboard Routes
{
  path: 'owner-dashboard',
  component: OwnerDashboardComponent,
  canActivate: [LoginGuard, roleGuard],
  data: { expectedRole: 'owner' }
},
{
  path: 'licensed-admins',
  component: LicensedAdminsComponent,
  canActivate: [LoginGuard, roleGuard],
  data: { expectedRole: 'owner' }
},
{
  path: 'owner-revenue',
  component: OwnerRevenueComponent,
  canActivate: [LoginGuard, roleGuard],
  data: { expectedRole: 'owner' }
},
{
  path: 'expiring-licenses',
  component: ExpiringLicensesComponent,
  canActivate: [LoginGuard, roleGuard],
  data: { expectedRole: 'owner' }
}
```

SIDEBAR.COMPONENT.HTML GÜNCELLEMELERİ:

Spor Salonu (gymMenuOpen) dropdown'ına eklenecek linkler:

```html
<!-- Mevcut linklerden ÖNCE ekle -->
<a class="menu-item" routerLink="owner-dashboard" routerLinkActive="active" *ngIf="isOwner">
  <i class="fas fa-tachometer-alt"></i>
  <span *ngIf="!collapsed">Owner Dashboard</span>
</a>

<a class="menu-item" routerLink="licensed-admins" routerLinkActive="active" *ngIf="isOwner">
  <i class="fas fa-users-cog"></i>
  <span *ngIf="!collapsed">Aktif Salonlar</span>
</a>

<a class="menu-item" routerLink="owner-revenue" routerLinkActive="active" *ngIf="isOwner">
  <i class="fas fa-chart-line"></i>
  <span *ngIf="!collapsed">Gelir Takibi</span>
</a>

<a class="menu-item" routerLink="expiring-licenses" routerLinkActive="active" *ngIf="isOwner">
  <i class="fas fa-clock"></i>
  <span *ngIf="!collapsed">Yakında Bitecek Lisanslar</span>
</a>

<!-- Ayırıcı çizgi -->
<div class="menu-divider" *ngIf="isOwner"></div>
```

AUTH.SERVICE.TS GÜNCELLEMELERİ:

Owner login sonrası yönlendirme güncelle:

```typescript
// Login başarılı sonrası (mevcut kodu değiştir)
if (this.hasRole('owner')) {
  response.redirectUrl = '/owner-dashboard';
} else if (this.hasRole('admin')) {
  response.redirectUrl = '/todayentries';
} else if (this.hasRole('member')) {
  response.redirectUrl = '/my-qr';
}
```

APP.MODULE.TS GÜNCELLEMELERİ:

Yeni component'leri declarations'a ekle:

```typescript
declarations: [
  // ... mevcut component'ler
  OwnerDashboardComponent,
  LicensedAdminsComponent,
  AdminDetailDialogComponent,
  OwnerRevenueComponent,
  ExpiringLicensesComponent,
  // ... diğer component'ler
]
```

BREADCRUMB NAVİGASYONU:
- Ana Sayfa > Owner Dashboard
- Ana Sayfa > Owner Dashboard > Aktif Salonlar
- Ana Sayfa > Owner Dashboard > Gelir Takibi
- Ana Sayfa > Owner Dashboard > Yakında Bitecek Lisanslar

================================================================================
PROMPT 9: CSS VE TEMA GÜNCELLEMELERİ
================================================================================

GÖREV: Owner dashboard için CSS stilleri ve tema güncellemelerini yap.

GLOBAL STYLES GÜNCELLEMELERİ (src/styles.css):

1. DASHBOARD KARTLARI:
```css
.owner-dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.owner-dashboard-card {
  background: var(--card-bg-color);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 6px var(--shadow-color);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
}

.owner-dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px var(--shadow-color);
}

.dashboard-card-icon {
  font-size: 2.5rem;
  margin-bottom: 16px;
  opacity: 0.8;
}

.dashboard-card-number {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 8px;
  color: var(--text-color);
}

.dashboard-card-label {
  font-size: 0.9rem;
  opacity: 0.7;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: var(--text-muted);
}

.dashboard-card-primary { border-left: 4px solid var(--primary-color); }
.dashboard-card-success { border-left: 4px solid #28a745; }
.dashboard-card-warning { border-left: 4px solid #ffc107; }
.dashboard-card-danger { border-left: 4px solid #dc3545; }
```

2. RENK KODLAMASI:
```css
.status-critical {
  color: #dc3545;
  background: rgba(220, 53, 69, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 500;
}

.status-warning {
  color: #fd7e14;
  background: rgba(253, 126, 20, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 500;
}

.status-attention {
  color: #ffc107;
  background: rgba(255, 193, 7, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 500;
}

.status-normal {
  color: #28a745;
  background: rgba(40, 167, 69, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 500;
}
```

3. RESPONSIVE DESIGN:
```css
@media (max-width: 768px) {
  .owner-dashboard-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .dashboard-card-number {
    font-size: 2rem;
  }

  .dashboard-card-icon {
    font-size: 2rem;
  }
}

@media (max-width: 576px) {
  .owner-dashboard-card {
    padding: 16px;
  }

  .dashboard-card-number {
    font-size: 1.8rem;
  }
}
```

COMPONENT-SPECIFIC STYLES:

1. OwnerDashboardComponent:
   - Grid layout (2x2)
   - Card hover effects
   - Loading skeletons

2. LicensedAdminsComponent:
   - Table styling (modern-table)
   - Filter section
   - Action buttons

3. AdminDetailDialogComponent:
   - Modal styling (95% width, 85vh height)
   - Tab navigation
   - Profile image styling

4. OwnerRevenueComponent:
   - Chart containers
   - Stats cards
   - Export buttons

5. ExpiringLicensesComponent:
   - Color-coded rows
   - Bulk action toolbar
   - Reminder buttons

DARK/LIGHT THEME SUPPORT:
```css
[data-theme="dark"] {
  .owner-dashboard-card {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
  }

  .dashboard-card-number,
  .dashboard-card-label {
    color: var(--text-primary);
  }
}
```

SIDEBAR MENU DIVIDER:
```css
.menu-divider {
  height: 1px;
  background-color: var(--border-color);
  margin: 8px 16px;
  opacity: 0.5;
}
```

================================================================================
PROMPT 10: TEST VE DEPLOYMENT REHBERİ
================================================================================

GÖREV: Owner dashboard özelliklerinin test edilmesi ve deployment rehberi.

UNIT TEST'LER:

1. OwnerDashboardComponent:
   - Dashboard stats yükleme testi
   - Kart tıklama navigation testi
   - Loading state testi
   - Error handling testi

2. LicensedAdminsComponent:
   - Admin listesi yükleme testi
   - Filtreleme testi
   - Pagination testi
   - CRUD işlem testleri

3. Services:
   - API çağrı testleri
   - Error handling testleri
   - Data transformation testleri

INTEGRATION TEST'LER:

1. End-to-End Test Senaryoları:
   - Owner login → Dashboard görüntüleme
   - Admin listesi → Detay modal → Güncelleme
   - Gelir sayfası → Filtreleme → Export
   - Lisans takibi → Uzatma → Bildirim

2. API Test'leri:
   - Endpoint response testleri
   - Authorization testleri
   - Performance testleri

DEPLOYMENT ADIMLARI:

1. Backend Deployment:
   - Database migration'ları çalıştır
   - Yeni controller'ları deploy et
   - API endpoint'lerini test et

2. Frontend Deployment:
   - ng build --prod
   - Asset optimization
   - Route testing

3. Production Checklist:
   - Owner role permissions kontrolü
   - API rate limiting
   - Error logging
   - Performance monitoring

MONITORING VE ANALYTICS:

1. Performance Metrics:
   - Dashboard load time (<2 saniye)
   - API response times (<500ms)
   - User interaction tracking

2. Error Tracking:
   - Frontend error logging
   - Backend exception handling
   - User feedback collection

3. Usage Analytics:
   - Feature usage statistics
   - User journey tracking
   - Conversion metrics

MAINTENANCE:

1. Regular Updates:
   - Security patches
   - Performance optimizations
   - Feature enhancements

2. Data Backup:
   - Dashboard configuration backup
   - User preferences backup
   - Analytics data backup

3. Support Documentation:
   - User manual
   - Admin guide
   - Troubleshooting guide

================================================================================
PROMPT KULLANIM REHBERİ
================================================================================

Bu prompt'ları kullanırken:

1. **Sıralı İlerleme**: Prompt 1'den başlayın, sırayla ilerleyin
2. **Tam Prompt Verme**: Her prompt'u AI'ya tam olarak kopyalayın
3. **Test Etme**: Her prompt sonrası çıktıyı test edin
4. **Hata Durumu**: Hata çıkarsa ilgili prompt'u tekrar kullanın
5. **Özelleştirme**: İhtiyaca göre prompt'ları modifiye edin

**ÖNEMLİ NOTLAR:**
- Her prompt bağımsız çalışabilir ancak sıralı kullanım önerilir
- Mevcut proje yapısına uygun kod üretilecek
- Modern CSS class'ları kullanılacak
- Dark mode desteği dahil edilecek
- Responsive tasarım uygulanacak

**PROJE YAPISI REFERANSLARI:**
- Frontend: Angular 17.3.6, TypeScript, Bootstrap
- Backend: .NET Core, Entity Framework, JWT
- CSS: modern-card, modern-btn, modern-table
- Tasarım: allmembers, memberfilter component'leri
- Modal: member-detail-dialog component'i

================================================================================
DOKÜMAN SONU
================================================================================
